import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Parse form data from sendBeacon
    const formData = await request.formData()
    const sessionId = formData.get('session_id') as string
    const pagePath = formData.get('page_path') as string
    const duration = parseInt(formData.get('duration') as string)

    if (!sessionId || !pagePath || isNaN(duration)) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Update the most recent page visit for this session and path
    const { error } = await supabase
      .from('page_visits')
      .update({ 
        visit_duration_seconds: duration,
        is_exit_page: true 
      })
      .eq('session_id', sessionId)
      .eq('page_path', pagePath)
      .order('created_at', { ascending: false })
      .limit(1)

    if (error) {
      console.error('Error updating page duration:', error)
      return NextResponse.json({ error: 'Failed to update page duration' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in page duration API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
