# PrimeCaffe Analytics System

## Overview

The PrimeCaffe Analytics System provides comprehensive visitor tracking and analytics for the e-commerce platform. It includes privacy-compliant visitor tracking, detailed analytics dashboards, and performance-optimized data aggregation.

## Features

### 🔍 Visitor Analytics
- **Real-time visitor tracking** with session management
- **Page view analytics** with duration tracking
- **Browser language breakdown** with visual charts
- **Top pages analysis** with filtering capabilities
- **Bounce rate and session duration** metrics

### 🛡️ Privacy Compliance
- **GDPR/Swiss data protection compliant**
- **Cookie consent integration** - respects user preferences
- **Anonymous data collection** - no personal information stored
- **Opt-out functionality** through cookie settings
- **Admin user exclusion** - admin/developer accounts are automatically excluded from analytics for accurate data

### 📊 Dashboard Features
- **Responsive design** optimized for desktop and mobile
- **Real-time data updates** with caching for performance
- **Multi-language support** (German, Italian, French)
- **Date range filtering** with preset options
- **Export functionality** for data analysis

## Architecture

### Database Schema

#### `visitor_sessions`
Tracks individual visitor sessions with anonymous identifiers.

```sql
- id: UUID (Primary Key)
- session_id: TEXT (Unique anonymous identifier)
- user_id: UUID (Optional, for filtering admin users)
- browser_language: TEXT
- user_agent: TEXT
- referrer: TEXT
- country_code: TEXT
- first_visit_at: TIMESTAMP
- last_activity_at: TIMESTAMP
- page_views_count: INTEGER
- is_bounce: BOOLEAN
- session_duration_seconds: INTEGER
```

#### `page_visits`
Records individual page visits within sessions.

```sql
- id: UUID (Primary Key)
- session_id: TEXT (References visitor_sessions)
- page_path: TEXT
- page_title: TEXT
- page_locale: TEXT
- browser_language: TEXT
- referrer: TEXT
- visit_duration_seconds: INTEGER
- is_exit_page: BOOLEAN
```

#### `visitor_analytics`
Pre-aggregated analytics data for performance optimization.

```sql
- id: UUID (Primary Key)
- date_period: DATE
- period_type: TEXT ('daily', 'monthly', 'yearly')
- total_visitors: INTEGER
- total_page_views: INTEGER
- unique_visitors: INTEGER
- bounce_rate: DECIMAL
- avg_session_duration: DECIMAL
- language_breakdown: JSONB
- top_pages: JSONB
- country_breakdown: JSONB
```

### Components

#### Core Components
- `VisitorTracker` - Client-side tracking component
- `AnalyticsOverview` - Main dashboard component
- `VisitorStatistics` - Visitor metrics display
- `LanguageAnalytics` - Language breakdown charts
- `PageAnalytics` - Page visit analysis with filtering

#### Service Layer
- `AnalyticsService` - Data processing and caching service
- API endpoints for data aggregation and tracking

## Installation & Setup

### 1. Database Setup
The analytics tables are automatically created when the visitor tracker is first used. The database schema includes:
- Row Level Security (RLS) policies
- Performance indexes
- Aggregation functions

### 2. Environment Variables
No additional environment variables required. The system uses existing Supabase configuration.

### 3. Component Integration
The `VisitorTracker` component is automatically included in the main layout:

```tsx
import { VisitorTracker } from '@/components/analytics/visitor-tracker'

// In your layout
<VisitorTracker />
```

## Usage

### Admin Dashboard
Access the analytics dashboard at `/admin/analytics`. The dashboard includes:

1. **Overview Tab** - Key metrics and summary charts
2. **Visitors Tab** - Detailed visitor analytics
3. **Languages Tab** - Browser language breakdown
4. **Pages Tab** - Page visit analysis with filtering

### Date Range Selection
Use the date range selector to filter data:
- Today, Yesterday
- Last 7/30 days
- This/Last month
- Last 3/6 months
- This year

### Language Filtering
Filter page analytics by browser language to understand content performance across different user segments.

## Privacy & Compliance

### GDPR Compliance
- **Lawful basis**: Legitimate interest for website analytics
- **Data minimization**: Only essential analytics data collected
- **Anonymization**: No personal identifiers stored
- **User control**: Respects cookie consent preferences
- **Data retention**: Configurable retention periods

### Cookie Integration
The system integrates with the existing cookie banner:
- Tracks only when analytics cookies are enabled
- Respects "necessary only" cookie settings
- Provides clear opt-out mechanism

### Data Security
- **Row Level Security** on all analytics tables
- **Admin-only access** to analytics data
- **Encrypted data transmission**
- **Secure session management**
- **Admin user filtering** - Automatically excludes admin/developer accounts:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>

## Performance Optimization

### Caching Strategy
- **In-memory caching** with 5-minute TTL
- **Automatic cache invalidation** on data updates
- **Efficient database queries** with proper indexing

### Data Aggregation
- **Background aggregation** for historical data
- **Real-time updates** for current metrics
- **Optimized queries** for large datasets

### API Endpoints
- `POST /api/analytics/page-duration` - Track page exit duration
- `POST /api/analytics/aggregate` - Update aggregated data
- `GET /api/analytics/aggregate` - Check aggregation status

## Testing

### Unit Tests
Comprehensive test coverage includes:
- Analytics service functionality
- Component rendering and interactions
- API endpoint behavior
- Error handling scenarios

### Running Tests
```bash
npm test analytics
```

## Monitoring & Maintenance

### Health Checks
Monitor the analytics system through:
- Database query performance
- Cache hit rates
- Error logs and tracking failures

### Data Maintenance
- Regular aggregation updates
- Cache optimization
- Performance monitoring

### Troubleshooting
Common issues and solutions:
- **No data appearing**: Check cookie consent settings
- **Slow dashboard**: Verify cache configuration
- **Missing translations**: Check language files

## Future Enhancements

### Planned Features
- **Geographic analytics** with country/region breakdown
- **Device type tracking** (mobile, desktop, tablet)
- **Referrer analysis** with traffic source categorization
- **Conversion funnel tracking**
- **A/B testing integration**

### Performance Improvements
- **Real-time streaming** for live analytics
- **Advanced caching** with Redis integration
- **Data warehouse** integration for historical analysis

## Support

For technical support or questions about the analytics system:
1. Check the troubleshooting section
2. Review error logs in the admin dashboard
3. Contact the development team

---

**Last Updated**: January 2024  
**Version**: 1.0.0  
**Compatibility**: Next.js 15, Supabase, TypeScript
