'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useTranslations } from 'next-intl'
import { 
  BarChart3, 
  Users, 
  FileText,
  TrendingUp,
  Eye,
  Languages,
  Calendar,
  RefreshCw,
  Download,
  Info
} from 'lucide-react'

import { VisitorStatistics } from './visitor-statistics'
import { LanguageAnalytics } from './language-analytics'
import { PageAnalytics } from './page-analytics'
import { TrafficSourceAnalytics } from './traffic-source-analytics'
import { DateRangeSelector, type DateRange } from './date-range-selector'

interface AnalyticsOverviewProps {
  className?: string
}

export function AnalyticsOverview({ className = '' }: AnalyticsOverviewProps) {
  const t = useTranslations('admin')
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>('thisMonth')
  const [refreshKey, setRefreshKey] = useState(0)
  const [activeTab, setActiveTab] = useState('overview')

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export analytics data')
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t('analytics.visitorAnalytics') || 'Analisi Visitatori'}
          </h2>
          <p className="text-muted-foreground">
            {t('analytics.visitorAnalyticsDescription') || 'Panoramica completa del traffico del sito web e del comportamento dei visitatori'}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <DateRangeSelector
            selectedRange={selectedDateRange}
            onRangeChange={setSelectedDateRange}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className="shrink-0"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="shrink-0"
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Info Banner */}
      <Card className="border-blue-200 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 shrink-0" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-blue-900">
                {t('analytics.privacyCompliant') || 'Tracciamento Conforme alla Privacy'}
              </p>
              <p className="text-xs text-blue-700">
                {t('analytics.privacyDescription') || 'Tutti i dati sono raccolti in modo anonimo e rispettano le impostazioni dei cookie degli utenti. Nessuna informazione personale viene memorizzata.'}
              </p>
              <p className="text-xs text-blue-600 font-medium mt-2">
                ℹ️ {t('analytics.adminExcluded') || 'Gli utenti admin/sviluppatori sono esclusi dalle analisi per garantire dati accurati.'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">{t('analytics.overview') || 'Panoramica'}</span>
          </TabsTrigger>
          <TabsTrigger value="visitors" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">{t('analytics.visitors') || 'Visitatori'}</span>
          </TabsTrigger>
          <TabsTrigger value="languages" className="flex items-center gap-2">
            <Languages className="h-4 w-4" />
            <span className="hidden sm:inline">{t('analytics.languages') || 'Lingue'}</span>
          </TabsTrigger>
          <TabsTrigger value="pages" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">{t('analytics.pages') || 'Pagine'}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <VisitorStatistics key={`visitor-stats-${refreshKey}`} />
          
          {/* Overview Grid */}
          <div className="grid lg:grid-cols-2 gap-6">
            <LanguageAnalytics
              key={`language-overview-${refreshKey}`}
              showTopCount={5}
            />
            <TrafficSourceAnalytics
              key={`traffic-overview-${refreshKey}`}
              showTopCount={5}
            />
          </div>

          {/* Page Analytics - Full Width */}
          <PageAnalytics
            key={`page-overview-${refreshKey}`}
            showTopCount={10}
          />
        </TabsContent>

        <TabsContent value="visitors" className="space-y-6">
          <VisitorStatistics key={`visitor-detailed-${refreshKey}`} />
          
          {/* Additional visitor metrics could go here */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t('analytics.visitorTrends') || 'Tendenze Visitatori'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>{t('analytics.comingSoon') || 'Grafici delle tendenze in arrivo'}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="languages" className="space-y-6">
          <LanguageAnalytics 
            key={`language-detailed-${refreshKey}`}
            showTopCount={15}
          />
        </TabsContent>

        <TabsContent value="pages" className="space-y-6">
          <PageAnalytics 
            key={`page-detailed-${refreshKey}`}
            showTopCount={25}
          />
        </TabsContent>
      </Tabs>

      {/* Footer Info */}
      <Card className="border-gray-200">
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>{t('analytics.realTimeData') || 'Dati in tempo reale'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span>{t('analytics.gdprCompliant') || 'Conforme GDPR'}</span>
              </div>
            </div>
            <div className="text-xs">
              {t('analytics.lastUpdated') || 'Ultimo aggiornamento'}: {new Date().toLocaleString('it-IT')}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
