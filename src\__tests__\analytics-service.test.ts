import { analyticsService } from '@/lib/analytics/analytics-service'
import type { AnalyticsDateRange } from '@/lib/analytics/analytics-service'

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        gte: jest.fn(() => ({
          lte: jest.fn(() => ({
            data: mockSessionsData,
            error: null
          }))
        })),
        eq: jest.fn(() => ({
          gte: jest.fn(() => ({
            lte: jest.fn(() => ({
              data: mockPageVisitsData,
              error: null
            }))
          }))
        }))
      }))
    })),
    rpc: jest.fn(() => ({ error: null }))
  }))
}))

// Mock data
const mockSessionsData = [
  {
    id: '1',
    session_id: 'session_1',
    browser_language: 'de-DE',
    page_views_count: 3,
    is_bounce: false,
    session_duration_seconds: 120,
    first_visit_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    session_id: 'session_2',
    browser_language: 'it-IT',
    page_views_count: 1,
    is_bounce: true,
    session_duration_seconds: 15,
    first_visit_at: '2024-01-15T11:00:00Z'
  },
  {
    id: '3',
    session_id: 'session_3',
    browser_language: 'fr-FR',
    page_views_count: 5,
    is_bounce: false,
    session_duration_seconds: 300,
    first_visit_at: '2024-01-15T12:00:00Z'
  }
]

const mockPageVisitsData = [
  {
    page_path: '/',
    page_title: 'Homepage',
    session_id: 'session_1',
    browser_language: 'de-DE',
    visit_duration_seconds: 60,
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    page_path: '/shop',
    page_title: 'Shop',
    session_id: 'session_1',
    browser_language: 'de-DE',
    visit_duration_seconds: 45,
    created_at: '2024-01-15T10:01:00Z'
  },
  {
    page_path: '/',
    page_title: 'Homepage',
    session_id: 'session_2',
    browser_language: 'it-IT',
    visit_duration_seconds: 15,
    created_at: '2024-01-15T11:00:00Z'
  }
]

describe('AnalyticsService', () => {
  const dateRange: AnalyticsDateRange = {
    start: new Date('2024-01-15T00:00:00Z'),
    end: new Date('2024-01-15T23:59:59Z')
  }

  beforeEach(() => {
    // Clear cache before each test
    analyticsService.clearCache()
    jest.clearAllMocks()
  })

  describe('getVisitorMetrics', () => {
    it('should calculate visitor metrics correctly', async () => {
      const metrics = await analyticsService.getVisitorMetrics(dateRange)

      expect(metrics).toEqual({
        totalVisitors: 3,
        totalPageViews: 9, // 3 + 1 + 5
        uniqueVisitors: 3,
        bounceRate: 33.33, // 1 out of 3 sessions is bounce
        avgSessionDuration: 145 // (120 + 15 + 300) / 3
      })
    })

    it('should handle empty data gracefully', async () => {
      // Mock empty response
      const mockSupabase = require('@/lib/supabase/client').createClient()
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          gte: jest.fn(() => ({
            lte: jest.fn(() => ({
              data: null,
              error: null
            }))
          }))
        }))
      })

      const metrics = await analyticsService.getVisitorMetrics(dateRange)

      expect(metrics).toEqual({
        totalVisitors: 0,
        totalPageViews: 0,
        uniqueVisitors: 0,
        bounceRate: 0,
        avgSessionDuration: 0
      })
    })

    it('should use cache for repeated requests', async () => {
      const mockSupabase = require('@/lib/supabase/client').createClient()
      
      // First call
      await analyticsService.getVisitorMetrics(dateRange)
      
      // Second call should use cache
      await analyticsService.getVisitorMetrics(dateRange)

      // Should only call the database once
      expect(mockSupabase.from).toHaveBeenCalledTimes(1)
    })
  })

  describe('getLanguageMetrics', () => {
    it('should calculate language metrics correctly', async () => {
      const metrics = await analyticsService.getLanguageMetrics(dateRange, 5)

      expect(metrics).toHaveLength(3)
      expect(metrics[0]).toMatchObject({
        language: expect.any(String),
        visitors: expect.any(Number),
        percentage: expect.any(Number),
        displayName: expect.any(String),
        flag: expect.any(String)
      })

      // Check that percentages add up to 100
      const totalPercentage = metrics.reduce((sum, m) => sum + m.percentage, 0)
      expect(totalPercentage).toBeCloseTo(100, 1)
    })

    it('should limit results correctly', async () => {
      const metrics = await analyticsService.getLanguageMetrics(dateRange, 2)
      expect(metrics).toHaveLength(2)
    })

    it('should handle unknown languages', async () => {
      // Mock data with unknown language
      const mockSupabase = require('@/lib/supabase/client').createClient()
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          gte: jest.fn(() => ({
            lte: jest.fn(() => ({
              data: [{ browser_language: 'unknown-lang' }],
              error: null
            }))
          }))
        }))
      })

      const metrics = await analyticsService.getLanguageMetrics(dateRange)
      expect(metrics[0].displayName).toBe('Sconosciuto')
      expect(metrics[0].flag).toBe('❓')
    })
  })

  describe('getPageMetrics', () => {
    it('should calculate page metrics correctly', async () => {
      const metrics = await analyticsService.getPageMetrics(dateRange, 10)

      expect(metrics).toHaveLength(2) // 2 unique pages
      expect(metrics[0]).toMatchObject({
        path: expect.any(String),
        title: expect.any(String),
        visits: expect.any(Number),
        uniqueVisitors: expect.any(Number),
        percentage: expect.any(Number),
        avgDuration: expect.any(Number)
      })

      // Should be sorted by visits descending
      if (metrics.length > 1) {
        expect(metrics[0].visits).toBeGreaterThanOrEqual(metrics[1].visits)
      }
    })

    it('should filter by language correctly', async () => {
      const mockSupabase = require('@/lib/supabase/client').createClient()
      const mockQuery = {
        select: jest.fn(() => ({
          gte: jest.fn(() => ({
            lte: jest.fn(() => ({
              eq: jest.fn(() => ({
                data: mockPageVisitsData.filter(v => v.browser_language === 'de-DE'),
                error: null
              }))
            }))
          }))
        }))
      }
      mockSupabase.from.mockReturnValue(mockQuery)

      await analyticsService.getPageMetrics(dateRange, 10, 'de-DE')

      expect(mockQuery.select().gte().lte().eq).toHaveBeenCalledWith('browser_language', 'de-DE')
    })

    it('should handle page title mapping', async () => {
      const metrics = await analyticsService.getPageMetrics(dateRange, 10)
      
      // Should have readable titles instead of just paths
      const homepageMetric = metrics.find(m => m.path === '/')
      expect(homepageMetric?.title).toBe('Homepage')
    })
  })

  describe('updateAnalyticsAggregation', () => {
    it('should call database function and clear cache', async () => {
      const mockSupabase = require('@/lib/supabase/client').createClient()
      const testDate = new Date('2024-01-15')

      await analyticsService.updateAnalyticsAggregation(testDate)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_visitor_analytics', {
        target_date: '2024-01-15'
      })
    })

    it('should handle database errors', async () => {
      const mockSupabase = require('@/lib/supabase/client').createClient()
      mockSupabase.rpc.mockReturnValue({ error: new Error('Database error') })

      await expect(analyticsService.updateAnalyticsAggregation()).rejects.toThrow('Database error')
    })
  })

  describe('Cache Management', () => {
    it('should clear cache correctly', () => {
      // This is more of an integration test to ensure cache clearing works
      expect(() => analyticsService.clearCache()).not.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const mockSupabase = require('@/lib/supabase/client').createClient()
      mockSupabase.from.mockImplementation(() => {
        throw new Error('Connection failed')
      })

      await expect(analyticsService.getVisitorMetrics(dateRange)).rejects.toThrow('Connection failed')
    })

    it('should handle malformed data gracefully', async () => {
      const mockSupabase = require('@/lib/supabase/client').createClient()
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          gte: jest.fn(() => ({
            lte: jest.fn(() => ({
              data: [{ invalid: 'data' }], // Malformed data
              error: null
            }))
          }))
        }))
      })

      const metrics = await analyticsService.getVisitorMetrics(dateRange)
      
      // Should handle gracefully and return sensible defaults
      expect(metrics.totalVisitors).toBe(1)
      expect(metrics.totalPageViews).toBe(0) // No page_views_count field
    })
  })
})
