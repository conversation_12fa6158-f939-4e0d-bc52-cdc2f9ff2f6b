import { createClient } from '@/lib/supabase/client'
import type { SupabaseClient } from '@supabase/supabase-js'

export interface AnalyticsDateRange {
  start: Date
  end: Date
}

export interface VisitorMetrics {
  totalVisitors: number
  totalPageViews: number
  uniqueVisitors: number
  bounceRate: number
  avgSessionDuration: number
}

export interface LanguageMetrics {
  language: string
  visitors: number
  percentage: number
  displayName: string
  flag: string
}

export interface PageMetrics {
  path: string
  title: string
  visits: number
  uniqueVisitors: number
  percentage: number
  avgDuration: number
}

class AnalyticsService {
  private supabase: SupabaseClient
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.supabase = createClient()
  }

  private getCacheKey(method: string, params: unknown): string {
    return `${method}_${JSON.stringify(params)}`
  }

  private isValidCache(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_TTL
  }

  private setCache(key: string, data: unknown): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private getCache(key: string): unknown | null {
    const cached = this.cache.get(key)
    if (cached && this.isValidCache(cached.timestamp)) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  async getVisitorMetrics(dateRange: AnalyticsDateRange): Promise<VisitorMetrics> {
    const cacheKey = this.getCacheKey('visitorMetrics', dateRange)
    const cached = this.getCache(cacheKey)
    if (cached) return cached as VisitorMetrics

    try {
      // Get sessions within date range
      const { data: sessions } = await this.supabase
        .from('visitor_sessions')
        .select('*')
        .gte('first_visit_at', dateRange.start.toISOString())
        .lte('first_visit_at', dateRange.end.toISOString())

      if (!sessions) {
        const emptyMetrics = {
          totalVisitors: 0,
          totalPageViews: 0,
          uniqueVisitors: 0,
          bounceRate: 0,
          avgSessionDuration: 0
        }
        this.setCache(cacheKey, emptyMetrics)
        return emptyMetrics
      }

      // Calculate metrics
      const totalVisitors = sessions.length
      const totalPageViews = sessions.reduce((sum, s) => sum + (s.page_views_count || 0), 0)
      const uniqueVisitors = new Set(sessions.map(s => s.session_id)).size
      const bounceRate = totalVisitors > 0 
        ? (sessions.filter(s => s.is_bounce).length / totalVisitors) * 100 
        : 0
      const avgSessionDuration = totalVisitors > 0
        ? sessions.reduce((sum, s) => sum + (s.session_duration_seconds || 0), 0) / totalVisitors
        : 0

      const metrics = {
        totalVisitors,
        totalPageViews,
        uniqueVisitors,
        bounceRate: Math.round(bounceRate * 100) / 100,
        avgSessionDuration: Math.round(avgSessionDuration)
      }

      this.setCache(cacheKey, metrics)
      return metrics
    } catch (error) {
      console.error('Error fetching visitor metrics:', error)
      throw error
    }
  }

  async getLanguageMetrics(dateRange: AnalyticsDateRange, limit: number = 10): Promise<LanguageMetrics[]> {
    const cacheKey = this.getCacheKey('languageMetrics', { dateRange, limit })
    const cached = this.getCache(cacheKey)
    if (cached) return cached as LanguageMetrics[]

    try {
      // Get sessions with language data
      const { data: sessions } = await this.supabase
        .from('visitor_sessions')
        .select('browser_language')
        .gte('first_visit_at', dateRange.start.toISOString())
        .lte('first_visit_at', dateRange.end.toISOString())

      if (!sessions) {
        this.setCache(cacheKey, [])
        return []
      }

      // Count by language
      const languageCounts: Record<string, number> = {}
      sessions.forEach(session => {
        const lang = session.browser_language || 'unknown'
        languageCounts[lang] = (languageCounts[lang] || 0) + 1
      })

      const total = sessions.length
      const languageMap = this.getLanguageMap()

      // Convert to metrics array
      const metrics = Object.entries(languageCounts)
        .map(([language, visitors]) => {
          const langInfo = this.getLanguageInfo(language, languageMap)
          return {
            language,
            visitors,
            percentage: total > 0 ? (visitors / total) * 100 : 0,
            displayName: langInfo.name,
            flag: langInfo.flag
          }
        })
        .sort((a, b) => b.visitors - a.visitors)
        .slice(0, limit)

      this.setCache(cacheKey, metrics)
      return metrics
    } catch (error) {
      console.error('Error fetching language metrics:', error)
      throw error
    }
  }

  async getPageMetrics(
    dateRange: AnalyticsDateRange,
    limit: number = 20,
    languageFilter?: string
  ): Promise<PageMetrics[]> {
    const cacheKey = this.getCacheKey('pageMetrics', { dateRange, limit, languageFilter })
    const cached = this.getCache(cacheKey)
    if (cached) return cached as PageMetrics[]

    try {
      // Build query
      let query = this.supabase
        .from('page_visits')
        .select('page_path, page_title, browser_language, visit_duration_seconds, session_id')
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString())

      if (languageFilter && languageFilter !== 'all') {
        query = query.eq('browser_language', languageFilter)
      }

      const { data: visits } = await query

      if (!visits) {
        this.setCache(cacheKey, [])
        return []
      }

      // Group by page path
      const pageStats: Record<string, {
        visits: number
        uniqueVisitors: Set<string>
        totalDuration: number
        title: string
      }> = {}

      visits.forEach(visit => {
        const path = visit.page_path
        if (!pageStats[path]) {
          pageStats[path] = {
            visits: 0,
            uniqueVisitors: new Set(),
            totalDuration: 0,
            title: visit.page_title || path
          }
        }

        pageStats[path].visits += 1
        pageStats[path].uniqueVisitors.add(visit.session_id)
        pageStats[path].totalDuration += visit.visit_duration_seconds || 0
      })

      const total = visits.length

      // Convert to metrics array
      const metrics = Object.entries(pageStats)
        .map(([path, stats]) => ({
          path,
          title: this.getPageDisplayName(path, stats.title),
          visits: stats.visits,
          uniqueVisitors: stats.uniqueVisitors.size,
          percentage: total > 0 ? (stats.visits / total) * 100 : 0,
          avgDuration: stats.visits > 0 ? Math.round(stats.totalDuration / stats.visits) : 0
        }))
        .sort((a, b) => b.visits - a.visits)
        .slice(0, limit)

      this.setCache(cacheKey, metrics)
      return metrics
    } catch (error) {
      console.error('Error fetching page metrics:', error)
      throw error
    }
  }

  async updateAnalyticsAggregation(date: Date = new Date()): Promise<void> {
    try {
      const targetDate = date.toISOString().split('T')[0] // YYYY-MM-DD format
      
      // Call the database function to update aggregated data
      const { error } = await this.supabase.rpc('update_visitor_analytics', {
        target_date: targetDate
      })

      if (error) {
        console.error('Error updating analytics aggregation:', error)
        throw error
      }

      // Clear related cache entries
      this.clearCacheByPattern('visitorMetrics')
      this.clearCacheByPattern('languageMetrics')
      this.clearCacheByPattern('pageMetrics')
    } catch (error) {
      console.error('Error in updateAnalyticsAggregation:', error)
      throw error
    }
  }

  private clearCacheByPattern(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key)
      }
    }
  }

  private getLanguageMap(): Record<string, { name: string; flag: string }> {
    return {
      'de': { name: 'Deutsch', flag: '🇩🇪' },
      'de-DE': { name: 'Deutsch', flag: '🇩🇪' },
      'de-CH': { name: 'Deutsch (Schweiz)', flag: '🇨🇭' },
      'it': { name: 'Italiano', flag: '🇮🇹' },
      'it-IT': { name: 'Italiano', flag: '🇮🇹' },
      'it-CH': { name: 'Italiano (Svizzera)', flag: '🇨🇭' },
      'fr': { name: 'Français', flag: '🇫🇷' },
      'fr-FR': { name: 'Français', flag: '🇫🇷' },
      'fr-CH': { name: 'Français (Suisse)', flag: '🇨🇭' },
      'en': { name: 'English', flag: '🇬🇧' },
      'en-US': { name: 'English (US)', flag: '🇺🇸' },
      'en-GB': { name: 'English (UK)', flag: '🇬🇧' },
      'unknown': { name: 'Sconosciuto', flag: '❓' }
    }
  }

  private getLanguageInfo(langCode: string, languageMap: Record<string, { name: string; flag: string }>): { name: string; flag: string } {
    if (languageMap[langCode]) {
      return languageMap[langCode]
    }
    
    const baseLang = langCode.split('-')[0]
    if (languageMap[baseLang]) {
      return languageMap[baseLang]
    }
    
    return languageMap['unknown']
  }

  private getPageDisplayName(path: string, title: string): string {
    if (title && title !== path && !title.includes('PrimeCaffe')) {
      return title
    }

    const pathMap: Record<string, string> = {
      '/': 'Homepage',
      '/de': 'Homepage (DE)',
      '/it': 'Homepage (IT)',
      '/fr': 'Homepage (FR)',
      '/shop': 'Shop',
      '/de/shop': 'Shop (DE)',
      '/it/shop': 'Shop (IT)',
      '/fr/shop': 'Shop (FR)',
      '/builder': 'Coffee Box Builder',
      '/de/builder': 'Coffee Box Builder (DE)',
      '/it/builder': 'Coffee Box Builder (IT)',
      '/fr/builder': 'Coffee Box Builder (FR)'
    }

    return pathMap[path] || path
  }

  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService()
